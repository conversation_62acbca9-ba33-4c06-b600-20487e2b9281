/**
 * Todo 任务管理模块
 * 负责任务的 CRUD 操作和查询功能
 */

const { API_CONFIG, TASK_CONFIG, ERROR_CODES } = require('./config')
const {
  createSuccessResponse,
  createErrorResponse,
  validateParams,
  formatDateForApi,
  simplifyTaskData,
  removeEmptyFields,
} = require('./utils')

/**
 * 任务管理类
 */
class TaskManager {
  constructor(authManager) {
    this.authManager = authManager
    console.log('[TaskManager] 初始化完成')
  }

  /**
   * 获取任务列表
   * @param {object} options - 查询参数
   * @returns {object} 任务列表
   */
  async getTasks(options = {}) {
    const {
      mode = 'all',
      keyword = null,
      priority = null,
      projectName = null,
      completed = null,
      limit = null,
      offset = 0,
    } = options

    console.log('[TaskManager.getTasks] 入参:', JSON.stringify(options))

    try {
      const batchResult = await this.authManager.getBatchData()
      if (batchResult.errCode) {
        console.error('[TaskManager.getTasks] 错误:', batchResult.errMsg)
        return batchResult
      }

      const { tasks, projects } = batchResult.data
      let filteredTasks = []

      // 处理时间筛选
      const now = new Date()
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
      const sevenDaysAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)

      for (const task of tasks) {
        // 只处理文本类型的任务
        if (task.kind !== TASK_CONFIG.KIND.TEXT) continue

        // 时间筛选
        if (mode !== TASK_CONFIG.FILTER_MODE.ALL) {
          const taskDate = task.modifiedTime ? new Date(task.modifiedTime) : null
          if (!taskDate) continue

          const taskDay = new Date(taskDate.getFullYear(), taskDate.getMonth(), taskDate.getDate())

          if (mode === TASK_CONFIG.FILTER_MODE.TODAY && taskDay.getTime() !== today.getTime()) continue
          if (mode === TASK_CONFIG.FILTER_MODE.YESTERDAY && taskDay.getTime() !== yesterday.getTime()) continue
          if (mode === TASK_CONFIG.FILTER_MODE.RECENT_7_DAYS && taskDay < sevenDaysAgo) continue
        }

        // 完成状态筛选
        if (completed !== null) {
          const isCompleted = task.status === TASK_CONFIG.STATUS.COMPLETED || task.isCompleted
          if (completed !== isCompleted) continue
        }

        // 优先级筛选（宽松：支持字符串/数字）
        if (priority !== null) {
          const priorityFilter = Number(priority)
          if (!Number.isNaN(priorityFilter)) {
            if (Number(task.priority) !== priorityFilter) continue
          } else {
            // 无法解析为数字时，回退为严格比较
            if (task.priority !== priority) continue
          }
        }

        // 关键词筛选
        if (keyword) {
          const searchText = `${task.title || ''} ${task.content || ''}`.toLowerCase()
          if (!searchText.includes(keyword.toLowerCase())) continue
        }

        // 项目名称筛选
        if (projectName) {
          const project = projects.find((p) => p.id === task.projectId)
          if (!project || !project.name.toLowerCase().includes(projectName.toLowerCase())) continue
        }

        // 简化任务数据
        const simplifiedTask = simplifyTaskData(task)

        // 添加项目信息
        if (task.projectId) {
          const project = projects.find((p) => p.id === task.projectId)
          if (project) {
            simplifiedTask.projectName = project.name
            simplifiedTask.projectColor = project.color
          }
        }

        filteredTasks.push(simplifiedTask)
      }

      // 分页/裁剪（最佳实践：由工具层处理）
      let slicedTasks = filteredTasks
      const normalizedOffset = Number.isInteger(offset) && offset > 0 ? offset : 0
      if (Number.isInteger(limit) && limit > 0) {
        slicedTasks = filteredTasks.slice(normalizedOffset, normalizedOffset + limit)
      } else if (normalizedOffset > 0) {
        slicedTasks = filteredTasks.slice(normalizedOffset)
      }

      const result = createSuccessResponse('获取任务列表成功', slicedTasks)
      console.log(
        '[TaskManager.getTasks] 出参:',
        JSON.stringify({ success: true, taskCount: slicedTasks.length, total: filteredTasks.length })
      )
      return result
    } catch (error) {
      console.error('[TaskManager.getTasks] 错误:', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '获取任务列表失败', error)
    }
  }

  /**
   * 创建任务
   * @param {object} options - 任务数据
   * @returns {object} 创建结果
   */
  async createTask(options = {}) {
    // 使用安全的参数处理方式，类似 Python 版本
    const {
      title = null,
      content = null,
      priority = null,
      projectName = null,
      tagNames = null,
      startDate = null,
      dueDate = null,
      isAllDay = null,
      reminder = null,
      kind = null,
    } = options

    console.log(
      '[TaskManager.createTask] 入参:',
      JSON.stringify({ title, content, priority, projectName, tagNames, startDate, dueDate, isAllDay, reminder, kind })
    )

    // 参数校验
    const validation = validateParams({ title }, ['title'])
    if (validation) {
      console.warn('[TaskManager.createTask] 参数校验失败:', validation.errMsg)
      return validation
    }

    try {
      // 获取基础数据以查找项目和标签
      const batchResult = await this.authManager.getBatchData()
      if (batchResult.errCode) {
        console.error('[TaskManager.createTask] 错误:', batchResult.errMsg)
        return batchResult
      }

      const { projects, tags } = batchResult.data
      let projectId = null
      let tagIds = []

      // 查找项目 ID（如果指定了项目名称）
      if (projectName !== null) {
        if (projectName) {
          const project = projects.find((p) => p.name.toLowerCase() === projectName.toLowerCase())
          if (project) {
            projectId = project.id
          } else {
            console.error('[TaskManager.createTask] 错误:', `未找到项目：${projectName}`)
            return createErrorResponse(ERROR_CODES.PROJECT_NOT_FOUND, `未找到项目：${projectName}`)
          }
        }
      }

      // 查找标签 ID（如果指定了标签名称）
      if (tagNames !== null) {
        if (tagNames && Array.isArray(tagNames) && tagNames.length > 0) {
          for (const tagName of tagNames) {
            const tag = tags.find((t) => t.name.toLowerCase() === tagName.toLowerCase())
            if (tag) {
              tagIds.push(tag.id)
            } else {
              console.warn('[TaskManager.createTask] 警告:', `未找到标签：${tagName}`)
            }
          }
        }
      }

      // 使用安全的参数处理方式，类似 Python 版本
      const safeTaskData = {
        title: title,
        content: content !== null ? content : '',
        priority: priority !== null ? priority : 0,
        kind: kind !== null ? kind : 'TEXT',
        status: TASK_CONFIG.STATUS.ACTIVE,
        isAllDay: isAllDay !== null ? isAllDay : false,
      }

      // 添加可选字段（只有在明确指定时才添加）
      if (projectId !== null) safeTaskData.projectId = projectId
      if (tagIds.length > 0) safeTaskData.tags = tagIds

      // 处理开始日期
      if (startDate !== null) {
        const formattedStartDate = startDate ? formatDateForApi(startDate) : null
        safeTaskData.startDate = formattedStartDate
      }

      // 处理截止日期
      if (dueDate !== null) {
        const formattedDueDate = dueDate ? formatDateForApi(dueDate) : null
        safeTaskData.dueDate = formattedDueDate
      }

      if (reminder !== null) safeTaskData.reminder = reminder

      // 移除空值字段
      const cleanTaskData = removeEmptyFields(safeTaskData)

      // 发送创建请求
      const result = await this.authManager._request('POST', API_CONFIG.TASK_URL, cleanTaskData)

      if (result.errCode) {
        console.error('[TaskManager.createTask] 错误:', result.errMsg)
        return result
      }

      const finalResult = createSuccessResponse('任务创建成功', result.data)
      console.log(
        '[TaskManager.createTask] 出参:',
        JSON.stringify({ success: true, taskId: result.data?.id, title: result.data?.title })
      )
      return finalResult
    } catch (error) {
      console.error('[TaskManager.createTask] 错误:', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '创建任务失败', error)
    }
  }

  /**
   * 获取单个任务详情
   * @param {string} taskId - 任务 ID
   * @returns {object} 任务详情
   */
  async getTask(taskId) {
    console.log('[TaskManager.getTask] 入参:', JSON.stringify({ taskId }))

    // 参数校验
    const validation = validateParams({ taskId }, ['taskId'])
    if (validation) {
      console.warn('[TaskManager.getTask] 参数校验失败:', validation.errMsg)
      return validation
    }

    try {
      const batchResult = await this.authManager.getBatchData()
      if (batchResult.errCode) {
        console.error('[TaskManager.getTask] 错误:', batchResult.errMsg)
        return batchResult
      }

      const { tasks } = batchResult.data
      const task = tasks.find((t) => t.id === taskId)

      if (!task) {
        console.warn('[TaskManager.getTask] 警告:', `未找到任务：${taskId}`)
        return createErrorResponse(ERROR_CODES.TASK_NOT_FOUND, '任务不存在')
      }

      // 简化任务数据
      const simplifiedTask = simplifyTaskData(task)

      const result = createSuccessResponse('获取任务详情成功', simplifiedTask)
      console.log('[TaskManager.getTask] 出参:', JSON.stringify({ success: true, taskId: taskId }))
      return result
    } catch (error) {
      console.error('[TaskManager.getTask] 错误:', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '获取任务详情失败', error)
    }
  }

  /**
   * 更新任务
   * @param {string} taskId - 任务 ID
   * @param {object} updateData - 更新数据
   * @returns {object} 更新结果
   */
  async updateTask(taskId, updateData) {
    console.log('[TaskManager.updateTask] 入参:', JSON.stringify({ taskId, updateData }))

    // 参数校验
    const validation = validateParams({ taskId }, ['taskId'])
    if (validation) {
      console.warn('[TaskManager.updateTask] 参数校验失败:', validation.errMsg)
      return validation
    }

    try {
      // 首先获取现有任务数据
      const batchResult = await this.authManager.getBatchData()
      if (batchResult.errCode) {
        console.error('[TaskManager.updateTask] 错误:', batchResult.errMsg)
        return batchResult
      }

      const { tasks, projects, tags } = batchResult.data

      // 查找现有任务
      const existingTask = tasks.find((t) => t.id === taskId)
      if (!existingTask) {
        console.error('[TaskManager.updateTask] 错误:', `未找到任务：${taskId}`)
        return createErrorResponse(ERROR_CODES.TASK_NOT_FOUND, `未找到任务：${taskId}`)
      }

      // 处理项目名称转换为项目 ID
      let projectId = updateData.projectId
      if (updateData.projectName !== undefined) {
        if (updateData.projectName) {
          const project = projects.find((p) => p.name.toLowerCase() === updateData.projectName.toLowerCase())
          if (project) {
            projectId = project.id
          } else {
            console.error('[TaskManager.updateTask] 错误:', `未找到项目：${updateData.projectName}`)
            return createErrorResponse(ERROR_CODES.PROJECT_NOT_FOUND, `未找到项目：${updateData.projectName}`)
          }
        } else {
          projectId = null
        }
      }

      // 处理标签名称转换为标签 ID
      let tagIds = updateData.tags
      if (updateData.tagNames !== undefined) {
        if (updateData.tagNames && Array.isArray(updateData.tagNames)) {
          tagIds = []
          for (const tagName of updateData.tagNames) {
            const tag = tags.find((t) => t.name.toLowerCase() === tagName.toLowerCase())
            if (tag) {
              tagIds.push(tag.id)
            } else {
              console.warn('[TaskManager.updateTask] 警告:', `未找到标签：${tagName}`)
            }
          }
        } else {
          tagIds = []
        }
      }

      // completed 语义映射到 status（最佳实践：由工具层处理）
      let mappedStatus = updateData.status
      if (updateData.completed !== undefined) {
        mappedStatus = updateData.completed ? TASK_CONFIG.STATUS.COMPLETED : TASK_CONFIG.STATUS.ACTIVE
      }

      // 使用安全的参数处理方式，类似 Python 版本
      const safeUpdateData = {
        id: taskId,
        title: updateData.title !== undefined ? updateData.title : existingTask.title,
        content: updateData.content !== undefined ? updateData.content : existingTask.content,
        priority: updateData.priority !== undefined ? updateData.priority : existingTask.priority,
        projectId: projectId !== undefined ? projectId : existingTask.projectId,
        tags: tagIds !== undefined ? tagIds : existingTask.tags || [],
        isAllDay: updateData.isAllDay !== undefined ? updateData.isAllDay : existingTask.isAllDay,
        status: mappedStatus !== undefined ? mappedStatus : existingTask.status,
        kind: updateData.kind !== undefined ? updateData.kind : existingTask.kind,
      }

      // 处理日期字段
      if (updateData.startDate !== undefined) {
        safeUpdateData.startDate = updateData.startDate ? formatDateForApi(updateData.startDate) : null
      } else if (existingTask.startDate) {
        safeUpdateData.startDate = existingTask.startDate
      }

      if (updateData.dueDate !== undefined) {
        safeUpdateData.dueDate = updateData.dueDate ? formatDateForApi(updateData.dueDate) : null
      } else if (existingTask.dueDate) {
        safeUpdateData.dueDate = existingTask.dueDate
      }

      // 处理提醒字段
      if (updateData.reminder !== undefined) {
        safeUpdateData.reminder = updateData.reminder
      } else if (existingTask.reminder) {
        safeUpdateData.reminder = existingTask.reminder
      }

      // 处理完成时间字段
      if (updateData.completedTime !== undefined) {
        safeUpdateData.completedTime = updateData.completedTime
      } else if (existingTask.completedTime) {
        safeUpdateData.completedTime = existingTask.completedTime
      }

      // 移除空值字段
      const cleanUpdateData = removeEmptyFields(safeUpdateData)

      // 发送更新请求
      const result = await this.authManager._request('POST', `${API_CONFIG.TASK_URL}/${taskId}`, cleanUpdateData)
      if (result.errCode) {
        console.error('[TaskManager.updateTask] 错误:', result.errMsg)
        return result
      }

      const finalResult = createSuccessResponse('任务更新成功', result.data)
      console.log('[TaskManager.updateTask] 出参:', JSON.stringify({ success: true, taskId: taskId }))
      return finalResult
    } catch (error) {
      console.error('[TaskManager.updateTask] 错误:', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '更新任务失败', error)
    }
  }

  /**
   * 删除任务
   * @param {string} taskId - 任务 ID
   * @returns {object} 删除结果
   */
  async deleteTask(taskId) {
    console.log('[TaskManager.deleteTask] 入参:', JSON.stringify({ taskId }))

    // 参数校验
    const validation = validateParams({ taskId }, ['taskId'])
    if (validation) {
      console.warn('[TaskManager.deleteTask] 参数校验失败:', validation.errMsg)
      return validation
    }

    try {
      // 发送删除请求
      const result = await this.authManager._request('DELETE', `${API_CONFIG.TASK_URL}/${taskId}`)
      if (result.errCode) {
        console.error('[TaskManager.deleteTask] 错误:', result.errMsg)
        return result
      }

      const finalResult = createSuccessResponse('任务删除成功', result.data)
      console.log('[TaskManager.deleteTask] 出参:', JSON.stringify({ success: true, taskId: taskId }))
      return finalResult
    } catch (error) {
      console.error('[TaskManager.deleteTask] 错误:', error)
      return createErrorResponse(ERROR_CODES.UNKNOWN_ERROR, error.message || '删除任务失败', error)
    }
  }
}

module.exports = TaskManager
