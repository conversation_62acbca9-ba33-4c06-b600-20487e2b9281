/**
 * AI 云对象主模块
 *
 * 提供智能任务执行和流式聊天功能：
 * - 任务意图识别和执行计划生成
 * - 多步骤任务的自动化执行
 * - 实时流式聊天响应
 * - Todo 工具集成和测试功能
 * - 执行上下文管理和数据传递
 */

/**
 * 执行上下文管理器
 * 用于管理任务执行过程中的所有数据和状态信息
 *
 * 数据结构：
 * - stepResults: Map<stepId, {result, metadata}> 存储步骤执行结果
 * - contextData: Map<key, value> 存储提取的上下文数据
 * - metadata: Object 存储执行过程的元数据信息
 *
 * 核心功能：
 * - 步骤结果管理：存储和检索每个步骤的执行结果
 * - 上下文数据提取：根据 AI 提取的实体，从结果中提取关键信息
 * - 项目智能匹配：基于 AI 提取的项目名称，匹配最相关的项目
 */
const { OpenAI } = require('openai')
const {
  FUNCTION_TOOLS,
  doubaoParams,
  SSE_MESSAGE_TYPES,
  createSSEMessage,
  generateSessionId,
} = require('./modules/config.js')
const TodoTool = require('./modules/todo')

/**
 * 流式工具调用处理（豆包模型优化版）
 */
async function handleStreamResponse(streamResponse, sseChannel, sessionId, originalMessages) {
  let pendingToolCalls = [] // 使用数组存储工具调用，支持索引
  let assistantMessage = ''
  let hasToolCalls = false

  for await (const chunk of streamResponse) {
    const delta = chunk.choices[0]?.delta
    const finishReason = chunk.choices[0]?.finish_reason

    // 处理普通文本内容
    if (delta?.content) {
      assistantMessage += delta.content
      await sseChannel.write(
        createSSEMessage(SSE_MESSAGE_TYPES.CHAT_CONTENT_CHUNK, sessionId, {
          content: delta.content,
          isComplete: false,
        })
      )
    }

    // 处理工具调用 - 豆包模型增量式处理
    if (delta?.tool_calls) {
      hasToolCalls = true

      for (const toolCallDelta of delta.tool_calls) {
        const index = toolCallDelta.index || 0
        const toolCallId = toolCallDelta.id

        // 初始化工具调用对象
        if (!pendingToolCalls[index]) {
          pendingToolCalls[index] = {
            id: toolCallId || `call_${Date.now()}_${index}`,
            type: 'function',
            function: {
              name: toolCallDelta.function?.name || '',
              arguments: toolCallDelta.function?.arguments || '',
            },
          }

          // 推送工具调用开始消息
          if (toolCallDelta.function?.name) {
            await sseChannel.write(
              createSSEMessage(SSE_MESSAGE_TYPES.TOOL_CALL_START, sessionId, {
                toolName: toolCallDelta.function.name,
                toolCallId: pendingToolCalls[index].id,
              })
            )
          }
        } else {
          // 累积工具调用参数
          if (toolCallDelta.function?.name) {
            pendingToolCalls[index].function.name = toolCallDelta.function.name
          }
          if (toolCallDelta.function?.arguments) {
            pendingToolCalls[index].function.arguments += toolCallDelta.function.arguments
          }
        }
      }
    }

    // 检查是否完成工具调用
    if (finishReason === 'tool_calls' && hasToolCalls) {
      // 执行所有完整的工具调用
      const toolResults = []

      for (const toolCall of pendingToolCalls.filter((tc) => tc && tc.function.name)) {
        try {
          const result = await executeToolCall(toolCall, sseChannel, sessionId)
          toolResults.push({
            toolCall: toolCall,
            result: result,
          })
        } catch (error) {
          console.error('工具调用执行失败：', error.message)
          await sseChannel.write(
            createSSEMessage(SSE_MESSAGE_TYPES.TOOL_EXECUTION_ERROR, sessionId, {
              toolName: toolCall.function.name,
              error: error.message,
            })
          )

          // 即使失败也要记录，以便后续处理
          toolResults.push({
            toolCall: toolCall,
            result: { error: error.message, success: false },
          })
        }
      }

      // 继续对话，让模型基于工具结果生成最终回复
      if (toolResults.length > 0) {
        await continueConversationWithToolResults(
          originalMessages,
          pendingToolCalls.filter((tc) => tc),
          toolResults,
          sseChannel,
          sessionId
        )
      }
    }

    // 处理普通对话结束
    if (finishReason === 'stop' && !hasToolCalls) {
      await sseChannel.write(
        createSSEMessage(SSE_MESSAGE_TYPES.CHAT_CONTENT_CHUNK, sessionId, {
          content: '',
          isComplete: true,
        })
      )
    }
  }
}

/**
 * 继续对话 - 将工具调用结果传回模型
 */
async function continueConversationWithToolResults(originalMessages, toolCalls, toolResults, sseChannel, sessionId) {
  const openai = new OpenAI(doubaoParams)

  // 构建包含工具调用和结果的完整消息历史
  const messagesWithToolResults = [
    ...originalMessages,
    // 添加助手的工具调用消息
    {
      role: 'assistant',
      content: null,
      tool_calls: toolCalls,
    },
    // 添加工具执行结果消息
    ...toolResults.map(({ toolCall, result }) => ({
      role: 'tool',
      tool_call_id: toolCall.id,
      content: JSON.stringify(result),
    })),
  ]

  // 推送工具结果处理开始消息
  await sseChannel.write(
    createSSEMessage(SSE_MESSAGE_TYPES.TOOL_RESULT_PROCESSING, sessionId, {
      message: '正在基于工具执行结果生成回复...',
    })
  )

  try {
    const followUpResponse = await openai.chat.completions.create({
      model: 'doubao-seed-1-6-flash-250715',
      messages: messagesWithToolResults,
      stream: true,
      timeout: 60000,
      thinking: { type: 'disabled' },
    })

    // 处理后续回复
    for await (const chunk of followUpResponse) {
      const delta = chunk.choices[0]?.delta
      const finishReason = chunk.choices[0]?.finish_reason

      if (delta?.content) {
        await sseChannel.write(
          createSSEMessage(SSE_MESSAGE_TYPES.CHAT_CONTENT_CHUNK, sessionId, {
            content: delta.content,
            isComplete: false,
          })
        )
      }

      if (finishReason === 'stop') {
        await sseChannel.write(
          createSSEMessage(SSE_MESSAGE_TYPES.CHAT_CONTENT_CHUNK, sessionId, {
            content: '',
            isComplete: true,
          })
        )
        break
      }
    }
  } catch (error) {
    console.error('工具结果处理失败：', error)
    await sseChannel.write(
      createSSEMessage(SSE_MESSAGE_TYPES.TOOL_RESULT_ERROR, sessionId, {
        error: '基于工具结果生成回复失败',
        details: error.message,
      })
    )
  }
}

/**
 * 统一工具执行接口
 */
async function executeToolCall(toolCall, sseChannel, sessionId) {
  const { function: func } = toolCall
  const toolName = func.name

  try {
    const parameters = JSON.parse(func.arguments)

    // 推送工具执行开始消息
    await sseChannel.write(
      createSSEMessage(SSE_MESSAGE_TYPES.TOOL_EXECUTION_START, sessionId, {
        toolName: toolName,
        parameters: parameters,
      })
    )

    let result
    const todoTool = new TodoTool()
    switch (toolName) {
      case 'getTasks':
        result = await todoTool.getTasks(parameters)
        break
      case 'createTask':
        result = await todoTool.createTask(parameters)
        break
      case 'getProjects':
        result = await todoTool.getProjects(parameters)
        break
      case 'updateTask': {
        const { taskId, ...updateData } = parameters || {}
        result = await todoTool.updateTask(taskId, updateData)
        break
      }
      case 'getCurrentTimeInfo':
        result = await executeGetCurrentTimeInfo(parameters)
        break
      default:
        throw new Error(`未知的工具：${toolName}`)
    }

    // 统一错误语义：工具层如返回 errCode，视为失败并抛错
    if (result && result.errCode !== undefined && result.errCode !== null && result.errCode !== 0) {
      throw new Error(result.errMsg || '工具执行失败')
    }

    // 推送工具执行完成消息
    await sseChannel.write(
      createSSEMessage(SSE_MESSAGE_TYPES.TOOL_EXECUTION_COMPLETE, sessionId, {
        toolName: toolName,
        result: result,
        success: true,
        toolCallId: toolCall.id,
      })
    )

    return result
  } catch (error) {
    // 推送工具执行失败消息
    await sseChannel.write(
      createSSEMessage(SSE_MESSAGE_TYPES.TOOL_EXECUTION_ERROR, sessionId, {
        toolName: toolName,
        error: error.message,
        success: false,
      })
    )

    throw error
  }
}

module.exports = {
  async chatStreamSSE({ channel, message, messages: history_records }) {
    const sessionId = generateSessionId()

    // 参数验证
    if (!message) {
      return {
        errCode: 'PARAM_IS_NULL',
        errMsg: '消息内容不能为空',
      }
    }

    if (!channel) {
      return {
        errCode: 'PARAM_IS_NULL',
        errMsg: 'SSE Channel 不能为空',
      }
    }

    try {
      const sseChannel = uniCloud.deserializeSSEChannel(channel)

      // 推送开始处理消息
      await sseChannel.write(
        createSSEMessage(SSE_MESSAGE_TYPES.PROCESSING_START, sessionId, {
          message: '开始处理您的请求...',
        })
      )

      // 初始化 AI 客户端
      const openai = new OpenAI(doubaoParams)

      // 🎯 获取当前真实时间信息（抽离为通用上下文）
      const { buildTimeContext } = require('./modules/time-context')
      const { timeInfo, raw } = buildTimeContext()
      const {
        tomorrow,
        yesterday,
        dayAfterTomorrow,
        dayBeforeYesterday,
        thisSaturday,
        thisSunday,
        nextMonday,
        lastDayOfMonth,
        firstDayOfNextMonth,
        lastDayOfNextMonth,
      } = raw

      // 构建消息数组 - 正确处理历史消息格式
      const { buildSystemPrompt } = require('./modules/prompts/prompt')
      const systemContent = buildSystemPrompt({
        timeInfo,
        dates: {
          tomorrow: tomorrow.toLocaleDateString('zh-CN'),
          yesterday: yesterday.toLocaleDateString('zh-CN'),
          dayAfterTomorrow: dayAfterTomorrow.toLocaleDateString('zh-CN'),
          dayBeforeYesterday: dayBeforeYesterday.toLocaleDateString('zh-CN'),
          thisSaturday: thisSaturday.toLocaleDateString('zh-CN'),
          thisSunday: thisSunday.toLocaleDateString('zh-CN'),
          nextMonday: nextMonday.toLocaleDateString('zh-CN'),
          lastDayOfMonth: lastDayOfMonth.toLocaleDateString('zh-CN'),
          firstDayOfNextMonth: firstDayOfNextMonth.toLocaleDateString('zh-CN'),
          lastDayOfNextMonth: lastDayOfNextMonth.toLocaleDateString('zh-CN'),
        },
      })

      const messages = [
        {
          role: 'system',
          content: systemContent,
        },
        // 正确处理历史消息，保留工具调用相关信息
        ...history_records.map((msg) => ({
          role: msg.role,
          content: msg.content,
          // 保留工具调用相关信息
          ...(msg.tool_calls && { tool_calls: msg.tool_calls }),
          ...(msg.tool_call_id && { tool_call_id: msg.tool_call_id }),
        })),
        {
          role: 'user',
          content: message,
        },
      ]

      // 创建流式响应
      const streamResponse = await openai.chat.completions.create({
        model: 'doubao-seed-1-6-flash-250715',
        messages: messages,
        tools: FUNCTION_TOOLS,
        tool_choice: 'auto',
        stream: true,
        timeout: 60000,
        thinking: { type: 'disabled' },
      })

      // 处理流式响应 - 传入原始消息用于工具调用后续处理
      await handleStreamResponse(streamResponse, sseChannel, sessionId, messages)

      // 推送会话结束消息
      await sseChannel.end(
        createSSEMessage(SSE_MESSAGE_TYPES.SESSION_END, sessionId, {
          message: '处理完成',
        })
      )

      return {
        errCode: 0,
        errMsg: 'success',
        data: {
          type: 'function_calling_complete',
          sessionId: sessionId,
        },
      }
    } catch (error) {
      console.error('chatStreamSSE 异常：', error.message)

      // 尝试推送错误消息
      try {
        const sseChannel = uniCloud.deserializeSSEChannel(channel)
        await sseChannel.end(
          createSSEMessage(SSE_MESSAGE_TYPES.ERROR, sessionId, {
            error: error.message,
            timestamp: new Date().toISOString(),
          })
        )
      } catch (channelError) {
        console.error('SSE 推送错误：', channelError)
      }

      return {
        errCode: 'SYSTEM_ERROR',
        errMsg: error.message || '系统处理失败',
        data: {
          type: 'system_error',
          sessionId: sessionId,
        },
      }
    }
  },
}

// 获取当前时间信息
async function executeGetCurrentTimeInfo(parameters) {
  console.log('[executeGetCurrentTimeInfo] 入参：', JSON.stringify(parameters))

  const { buildTimeContext } = require('./modules/time-context')
  const { now, timeInfo, dates, raw } = buildTimeContext()
  const format = parameters.format || 'detailed'

  let timeData = {}

  switch (format) {
    case 'iso':
      timeData = {
        current_datetime: now.toISOString(),
        today: now.toISOString().split('T')[0],
        tomorrow: raw.tomorrow.toISOString().split('T')[0],
        yesterday: raw.yesterday.toISOString().split('T')[0],
      }
      break

    case 'local':
      timeData = {
        current_date: now.toLocaleDateString('zh-CN'),
        current_time: now.toLocaleTimeString('zh-CN'),
        today: now.toLocaleDateString('zh-CN'),
        tomorrow: dates.tomorrow,
        yesterday: dates.yesterday,
      }
      break

    case 'detailed':
    default:
      timeData = {
        // 基础时间信息
        current_datetime: timeInfo.current_datetime,
        current_date: timeInfo.current_date,
        current_time: timeInfo.current_time,
        current_year: timeInfo.current_year,
        current_month: timeInfo.current_month,
        current_day: timeInfo.current_day,
        current_hour: timeInfo.current_hour,
        current_minute: timeInfo.current_minute,
        current_weekday: timeInfo.current_weekday,
        timezone: timeInfo.timezone,
        unix_timestamp: timeInfo.unix_timestamp,

        // 相对时间转换
        relative_times: {
          今天: dates.today || timeInfo.current_date,
          明天: dates.tomorrow,
          后天: dates.dayAfterTomorrow,
          昨天: dates.yesterday,
          前天: dates.dayBeforeYesterday,
          今晚: `${timeInfo.current_date} 23:59:59`,
          明天早上: `${dates.tomorrow} 09:00:00`,
          明天晚上: `${dates.tomorrow} 23:59:59`,
          本周末: dates.thisSaturday,
          这个周末: dates.thisSunday,
          下周一: dates.nextMonday,
          下周: dates.nextWeek,
          本月底: `${dates.lastDayOfMonth} 23:59:59`,
          下个月初: `${dates.firstDayOfNextMonth} 09:00:00`,
          下月底: `${dates.lastDayOfNextMonth} 23:59:59`,
        },

        // 格式化示例
        format_examples: {
          'YYYY-MM-DD': now.toISOString().split('T')[0],
          'YYYY-MM-DD HH:MM:SS': `${now.toISOString().split('T')[0]} ${now.toTimeString().split(' ')[0]}`,
          'MM-DD': `${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`,
          中文日期: timeInfo.current_date,
        },
      }
      break
  }

  const finalResult = {
    success: true,
    data: timeData,
    message: `成功获取当前时间信息（格式：${format}）`,
    current_time: now.toLocaleString('zh-CN'),
  }

  console.log('[executeGetCurrentTimeInfo] 出参：', JSON.stringify({ success: finalResult.success, format: format }))

  return finalResult
}
